import { Accordion, AccordionTab } from 'primereact/accordion';
import { Button } from 'primereact/button';
import { Calendar } from 'primereact/calendar';
import { Checkbox } from 'primereact/checkbox';
import { Dropdown } from 'primereact/dropdown';
import { InputNumber } from 'primereact/inputnumber';
import { InputText } from 'primereact/inputtext';
import { InputTextarea } from 'primereact/inputtextarea';
import { RadioButton } from 'primereact/radiobutton';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import React, { useEffect, useState } from 'react';
import APIServices from '../service/APIService';
import { API } from '../constants/api_url';
import useForceUpdate from "use-force-update";
import moment from "moment";

const SectionBox = ({ data, onUpdateResponse, userlist }) => {

  const [open, setOpen] = useState(false);
  const [inputValue, setInputValue] = useState(data.type === 'checkbox-group' ? [] : data.type === 'tableadd' ? [] : '');
  const [fieldName, setFieldName] = useState(null)
  const [consolidateText, setConsolidateText] = useState('');
  const forceUpdate = useForceUpdate();
  const getUser = (id) => {
    let user_name = 'Not Found'
    let index = userlist.findIndex(i => i.id === Number(id))
    if (index !== -1) {
      user_name = userlist[index].information.empname
    }
    return user_name
  }

  // TableAdd helper functions
  const addRow = (field) => {
    const current = inputValue || [];
    const updated = [...current, field.newrow[0]];
    setInputValue(updated);
    forceUpdate();
  }

  const deleteRow = (rowindex) => {
    const current = inputValue || [];
    const updated = current.filter((_, index) => index !== rowindex);
    setInputValue(updated);
    forceUpdate();
  }

  const getObjectAtIndex = (data, index) => {
    const keys = Object.keys(data);
    const key = keys[index];
    return data[key];
  };

  const onCellEditComplete = (e) => {
    try {
      let { rowData, newValue, cellIndex, field: columnField, rowIndex, headerIndex } = e;

      console.log('onCellEditComplete called:', { rowData, newValue, cellIndex, columnField, rowIndex, headerIndex });

      let actualValue = newValue !== undefined ? newValue : rowData[columnField];

      // Create a deep copy of the current input value with defensive coding
      let loc;
      try {
        loc = JSON.parse(JSON.stringify(inputValue || []));
      } catch (parseError) {
        console.error('Error parsing inputValue:', parseError);
        loc = inputValue || [];
      }
      setTimeout(() => {
        try {
          // The columnField is now the header name directly
          const headerName = columnField;
          console.log('Updating header:', headerName, 'with value:', actualValue);

          if (headerName && loc[rowIndex] && loc[rowIndex][headerName]) {
            if (!loc[rowIndex][headerName].data) {
              loc[rowIndex][headerName].data = {};
            }
            loc[rowIndex][headerName].data['value'] = actualValue;
            console.log('Updated cell data:', loc[rowIndex][headerName]);
          }

          // Clean up temporary field (like reference implementation)
          if (loc[rowIndex] && columnField !== headerName) {
            delete loc[rowIndex][columnField];
          }

          setInputValue(loc);
          forceUpdate();
        } catch (updateError) {
          console.error('Error updating cell data:', updateError);
        }
      }, 100);
    } catch (error) {
      console.error('Error in onCellEditComplete:', error);
    }
  };

  const renderTableData = (rowData) => {
    try {
      if (!rowData || !rowData.data) {
        return <div>No data</div>;
      }

      const isError = rowData.data.error === 1;
      const cellStyle = {
        color: isError ? 'red' : 'inherit',
        cursor: rowData.type === 5 ? 'default' : 'pointer',
        padding: '8px',
        minHeight: '30px',
        display: 'flex',
        alignItems: 'center',
        width: '100%'
      };

      const currentValue = rowData.data.value;

      if (rowData.type === 5) {
        return (
          <div style={cellStyle}>
            {rowData.data.label || 'Label'}
          </div>
        );
      }

      switch (rowData.type) {
        case 1:
        case 2:
        case 3:
          let displayValue;
          if (currentValue === undefined || currentValue === null || currentValue === '') {
            displayValue = 'click here';
          } else if (typeof currentValue === 'object') {
            displayValue = 'click here';  // Don't show object structure, just show placeholder
          } else {
            displayValue = String(currentValue);
          }
          return (
            <div style={cellStyle}>
              {displayValue}
            </div>
          );
        case 4:
          let selectedOption = null;
          try {
            selectedOption = rowData.data.values && currentValue !== null && currentValue !== undefined
              ? rowData.data.values.find((i) => i.value === currentValue)
              : null;
          } catch (error) {
            console.error('Error finding selected option:', error);
          }
          return (
            <div style={cellStyle}>
              {selectedOption ? selectedOption.label : 'Select option'}
            </div>
          );
        case 6:
          let dateDisplay = 'click here';
          try {
            if (currentValue !== undefined && currentValue !== null) {
              dateDisplay = moment(currentValue).format('DD-MM-YYYY');
            }
          } catch (error) {
            console.error('Error formatting date:', error);
            dateDisplay = 'Invalid date';
          }
          return (
            <div style={cellStyle}>
              {dateDisplay}
            </div>
          );
        default:
          let defaultDisplay;
          if (currentValue === undefined || currentValue === null || currentValue === '') {
            defaultDisplay = 'click here';
          } else if (typeof currentValue === 'object') {
            defaultDisplay = JSON.stringify(currentValue);
          } else {
            defaultDisplay = String(currentValue);
          }
          return (
            <div style={cellStyle}>
              {defaultDisplay}
            </div>
          );
      }
    } catch (error) {
      console.error('Error in renderTableData:', error);
      return (
        <div>Error rendering data</div>
      );
    }
  }

  const renderEditor = (options) => {
    try {
      // The field is now the header name directly
      const headerName = options.field;
      const item = options.rowData[headerName];

      if (!item) {
        console.warn('No item found for header:', headerName);
        return (
          <InputText
            type="text"
            defaultValue=""
            onChange={(e) => options.editorCallback(e.target.value)}
            autoFocus
            style={{ width: '100%' }}
          />
        );
      }

      // Extract the actual value from the data structure
      let initialValue = '';

      // First check if the value was passed directly in options (from our modified editor call)
      if (options.value !== undefined && options.value !== null && typeof options.value !== 'object') {
        initialValue = options.value;
      } else if (item.data && item.data.value !== undefined && item.data.value !== null) {
        initialValue = item.data.value;
      }

      // Convert initialValue to string for display, handle different types
      let displayValue = '';
      if (initialValue !== null && initialValue !== undefined) {
        if (typeof initialValue === 'object') {
          displayValue = '';  // Don't show objects
        } else {
          displayValue = String(initialValue);
        }
      }

      switch (item.type) {
        case 1:
          return (<InputText
            type="text"
            defaultValue={displayValue}
            onChange={(e) => options.editorCallback(e.target.value)}
            autoFocus
            style={{ width: '100%' }}
          />);
        case 2:
          return (<InputTextarea
            defaultValue={displayValue}
            onChange={(e) => options.editorCallback(e.target.value)}
            autoFocus
            style={{ width: '100%' }}
          />);
        case 3:
          return (<InputText
            type="number"
            defaultValue={displayValue}
            onChange={(e) => options.editorCallback(e.target.value)}
            autoFocus
            style={{ width: '100%' }}
          />);
        case 4:
          return (<Dropdown
            optionLabel="label"
            optionValue="value"
            value={initialValue}
            options={item.data?.values || []}
            onChange={(e) => options.editorCallback(e.value)}
            autoFocus
            style={{ width: '100%' }}
            panelStyle={{ zIndex: 9999 }}
          />);
        case 6:
          return (<Calendar
            dateFormat="dd/mm/yy"
            value={initialValue}
            onChange={(e) => options.editorCallback(e.value)}
            style={{ width: '100%' }}
            panelStyle={{ zIndex: 9999 }}
          />);
        case 5:
          return null;
        default:
          return (<InputText
            type="text"
            defaultValue={displayValue}
            onChange={(e) => options.editorCallback(e.target.value)}
            autoFocus
            style={{ width: '100%' }}
          />);
      }
    } catch (error) {
      console.error('Error in renderEditor:', error);
      return (
        <InputText
          type="text"
          value={options.value || ''}
          onChange={(e) => {
            try {
              options.editorCallback(e.target.value);
            } catch (err) {
              console.error('Error in fallback editor callback:', err);
            }
          }}
          autoFocus
          style={{ width: '100%' }}
        />
      );
    }
  }

  const actionTemplate = (rowData, e) => {
    return (
      <>
        <Button
          icon="pi pi-trash"
          className="mr-2 actionbtn"
          style={{
            width: '20px',
            height: '20px',
            background: 'transparent',
            color: 'palevioletred'
          }}
          onClick={() => {
            deleteRow(e.rowIndex)
          }}
        />
      </>
    )
  };



  const frameworkMap = {
    2: "GRI",
    3: "ISSB",
    4: "MCfS",
    5: "SASB",
    6: "TCFD",
    7: "BRSR",
    8: "SGX",
    9: "Boursa Kuwait",
    10: "Bursa Malaysia",
    11: "HKEX",
    12: "NASDAQ",
    13: "CDP",
    14: "EcoVadis",
    15: "CDP",
    16: "EcoVadis",
    17: "MSCI",
    18: "S&P Dow Jones",
    19: "Sustainalitics",
    20: "ISS",
    21: "Carbon Footprint",
    22: "GEF capital",
    24: "BRSR Core",
    25: "CSRD",
    26: "DJSI",
    27: "ESRS",
    28: "IFRS S1",
    29: "IFRS S2",
  };
  const defaultUser = 'Consolidate';
  // 👇 Mapping function
  const getDisplayType = (type) => {
    const typeMap = {
      text: 'descriptive',
      textarea: 'descriptive',
      checkbox: 'multi-select',
      'checkbox-group': 'multi-select',
      radio: 'single-select',
      'radio-group': 'single-select',
      'select-single': 'single-select',
      'select-multi': 'multi-select',
      file: 'file-upload',
      number: 'numeric',
      date: 'date',
      'datetime-local': 'date',
      tableadd: 'table-data',
    };
    return typeMap[type] || type;
  };
  useEffect(() => {
    const consolidate = Object.entries(data?.response || {}).flatMap(([a, b]) => b)?.find(x => x.user === 'Consolidate') || {}
    if (consolidate.answer != null) {
      if (['select', 'radio-group', 'checkpoint'].includes(data.type)) {
        consolidate.res = data.values?.find(x => x.label === consolidate.answer)?.value
      } else if (data.type === 'checkbox-group') {
        consolidate.res = data.values?.filter(x => consolidate?.answer?.includes(x.label)).map(x => x.value)
      } else if (data.type === 'tableadd') {
        consolidate.res = consolidate.answer || []
      } else {
        consolidate.res = consolidate.answer
      }
    }
    setConsolidateText(consolidate.comment)
    console.log(consolidate)
    setInputValue(consolidate ? consolidate?.res : (data.type === 'checkbox-group' ? [] : data.type === 'tableadd' ? [] : ''))
  }, [data])


  const displayType = getDisplayType(data.type);

  const getTagColor = (type) => {
    const display = getDisplayType(type);
    switch (display) {
      case 'single-select':
      case 'checkpoint':
        return ['#dbf7d7', '#61be53'];
      case 'file-upload':
        return ['#f4d8e9', '#8a276e'];
      case 'multi-select':
        return ['#cce5ff', '#004085'];
      case 'numeric':
        return ['#fbe8a6', '#a67c00'];
      case 'table-data':
        return ['#e6f3ff', '#0066cc'];
      default:
        return ['#f4e9c4', '#e99e34'];
    }
  };

  const handleSave = (question) => {
    let finalAnswer = inputValue;

    if (['select', 'radio-group', 'checkpoint'].includes(data.type)) {
      const selected = data.values?.find(opt => opt.label === inputValue || opt.value === inputValue);
      finalAnswer = selected?.label || '';
    }

    if (data.type === 'checkbox-group') {
      finalAnswer = inputValue.filter(val =>
        data.values?.some(opt => opt.label === val || opt.value === val)
      );
    }

    if (data.type === 'tableadd') {
      finalAnswer = inputValue || [];
    }

    console.log(finalAnswer, question.name)
    const newResponse = {
      user: defaultUser,
      answer: finalAnswer,
    };

    const filtered = (data.response || []).filter(r => r.user !== defaultUser);
    const updatedResponses = [...filtered, newResponse, {
      user: defaultUser,
      answer: consolidateText,
    }];

    onUpdateResponse({
      ...(finalAnswer != null && finalAnswer !== '' ? { [data.name]: finalAnswer } : {}),
      [`${data.name}_comments`]: consolidateText
    });

    setInputValue(data.type === 'checkbox-group' ? [] : data.type === 'tableadd' ? [] : '');
    setConsolidateText('');
  };

  const handleCancel = () => {
    setInputValue(data.type === 'checkbox-group' ? [] : data.type === 'tableadd' ? [] : '');
    setConsolidateText('');
  };

  const [bg, color] = getTagColor(data.type);

  const inputTypes = [
    'text', 'textarea', 'number', 'select',
    'radio-group', 'checkbox-group', 'file',
    'date', 'checkpoint', 'tableadd'
  ];

  const responsesToShow = (data.response && data.response.length > 0)
    ? data.response
    : []

  const renderAnswer = (answer) => {
    if (data.type === 'file') {
      return <a href={answer?.includes('api.eisqr.com') ? answer : API.Docs + answer} >{answer} </a>
    }

    if (data.type === 'tableadd') {
      if (!Array.isArray(answer) || answer.length === 0) {
        return 'No data available';
      }

      return (
        <div className="table-responsive">
          <DataTable
            value={answer}
            showGridlines
            className="p-datatable-sm"
            style={{ width: '100%' }}
            scrollable
            scrollHeight="200px"
          >
            {data.headers && data.headers.map((header, headerIndex) => (
              <Column
                key={headerIndex}
                field={header}
                header={header}
                body={(rowData) => {
                  const cellData = rowData[header];
                  if (!cellData || !cellData.data) return '-';

                  const value = cellData.data.value;

                  switch (cellData.type) {
                    case 1:
                    case 2:
                    case 3:
                      return value || '-';
                    case 4:
                      if (cellData.data.values && value !== null && value !== undefined) {
                        const selectedOption = cellData.data.values.find(opt => opt.value === value);
                        return selectedOption ? selectedOption.label : value;
                      }
                      return value || '-';
                    case 5:
                      return cellData.data.label || '-';
                    case 6:
                      return value ? moment(value).format('DD-MM-YYYY') : '-';
                    default:
                      return value || '-';
                  }
                }}
              />
            ))}
          </DataTable>
        </div>
      );
    }

    if (Array.isArray(answer)) {
      return answer
        .map(val => data.values?.find(opt => opt.value === val || opt.label === val)?.label || val)
        .filter(Boolean)
        .join(', ');
    }
    return data.values?.find(opt => opt.value === answer || opt.label === answer)?.label || answer;
  };

  if (data.type === 'paragraph') return null;

  return (
    <>
      <style>
        {`
          .p-datatable .p-datatable-tbody > tr > td .p-inputtext,
          .p-datatable .p-datatable-tbody > tr > td .p-inputtextarea,
          .p-datatable .p-datatable-tbody > tr > td .p-dropdown,
          .p-datatable .p-datatable-tbody > tr > td .p-calendar {
            width: 100% !important;
            min-width: 120px !important;
            z-index: 1000 !important;
            position: relative !important;
            background: white !important;
            border: 1px solid #ced4da !important;
          }

          .p-datatable .p-datatable-tbody > tr > td.p-cell-editing {
            padding: 2px !important;
            background: #f8f9fa !important;
          }

          .p-datatable .p-datatable-tbody > tr > td.p-cell-editing > * {
            width: 100% !important;
          }

          .p-dropdown-panel,
          .p-calendar-panel {
            z-index: 9999 !important;
          }

          .p-datatable .p-datatable-tbody > tr > td .p-dropdown .p-dropdown-trigger {
            background: transparent !important;
            border-left: 1px solid #ced4da !important;
          }

          .p-datatable .p-datatable-tbody > tr > td .p-dropdown:not(.p-disabled):hover {
            border-color: #007ad9 !important;
          }

          .p-datatable .p-datatable-tbody > tr > td .p-dropdown:not(.p-disabled).p-focus {
            outline: 0 none !important;
            outline-offset: 0 !important;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25) !important;
            border-color: #007ad9 !important;
          }

          .p-inputtextarea {
            resize: vertical !important;
            min-height: 60px !important;
          }

          .p-datatable .p-datatable-tbody > tr > td.p-editable-column {
            cursor: pointer !important;
          }

          .p-datatable .p-datatable-tbody > tr > td.p-cell-editing .p-dropdown,
          .p-datatable .p-datatable-tbody > tr > td.p-cell-editing .p-calendar {
            border: 2px solid #007ad9 !important;
          }

          .p-datatable .p-datatable-tbody > tr > td {
            padding: 4px !important;
          }

          .p-datatable .p-datatable-tbody > tr > td.p-cell-editing {
            padding: 2px !important;
          }

          .p-datatable .p-datatable-tbody > tr > td.p-editable-column:hover {
            background-color: #f8f9fa !important;
          }

          .p-datatable .p-datatable-tbody > tr > td.p-disabled {
            cursor: default !important;
            background-color: #f5f5f5 !important;
            color: #6c757d !important;
          }

          .p-datatable .p-datatable-tbody > tr > td > div {
            min-height: 30px;
            display: flex;
            align-items: center;
            width: 100%;
          }
        `}
      </style>

      <Accordion>
        <AccordionTab
          className='acc-pad'
          header={
            <div className="col-12 flex justify-content-between parent-full-width">
              <div className='col-9'>{data.label}</div>
              <div className='col-3 flex' >
                <span
                  className="badge d-inline-block text-capitalize"
                  style={{
                    backgroundColor: ['#f4e9c4', '#e99e34'],
                    color,
                    marginLeft: 'auto',
                    fontSize: '0.75rem',
                    padding: '6px 12px',
                    borderRadius: '5px',
                    minWidth: '100px',
                    textAlign: 'center'
                  }}
                >
                  {data.name}
                </span>
                <span
                  className="badge d-inline-block text-capitalize"
                  style={{
                    backgroundColor: bg,
                    color,
                    marginLeft: 'auto',
                    fontSize: '0.75rem',
                    padding: '6px 12px',
                    borderRadius: '5px',
                    minWidth: '100px',
                    textAlign: 'center'
                  }}
                >
                  {displayType}
                </span>

              </div>
            </div>
          }
        >
          <div className="accordion-body">
            <div className='flex justify-content-end m-2'>
              {data.assignedFramework && (
                <div className="d-flex flex-wrap gap-2 mt-2">
                  {Object.entries(data.assignedFramework).map(([frameworkId, labels]) =>
                    labels.map((labelVal, i) => (
                      <span
                        key={`${frameworkId}-${i}`}
                        className="badge bg-primary text-white px-2 py-1 rounded"
                        style={{ fontSize: "0.85rem" }}
                      >
                        {frameworkMap[frameworkId]} - {labelVal}
                      </span>
                    ))
                  )}
                </div>
              )}
            </div>
            {/* Response Table */}
            {inputTypes.includes(data.type) & data.type !== 'tableadd' ? <div className="table-responsive mb-3">
              <table className="table table-bordered">
                <thead className="table-light">
                  <tr>
                    <th>From</th>
                    <th>Entity</th>
                    <th>Response</th>
                  </tr>
                </thead>
                <tbody>
                  {responsesToShow.map((row, idx) =>
                    row.user !== 'Consolidate' ? (
                      <tr key={idx}>
                        <td>{getUser(Number(row.user))}</td>
                        <td>{row.entity}</td>
                        <td>{renderAnswer(row.answer)}</td>
                      </tr>
                    ) : null
                  )}
                </tbody>
              </table>
            </div> :

              <div className="mb-4">
                {(() => {
                  let tableHeaders = [];

                  // Extract headers for tableadd
                  if (responsesToShow && responsesToShow.length > 0) {
                    const firstResponse = responsesToShow.find(r => r.answer && Array.isArray(r.answer) && r.answer.length > 0);
                    if (firstResponse && firstResponse.answer[0]) {
                      tableHeaders = Object.keys(firstResponse.answer[0]);
                    }
                  }

                  return responsesToShow.filter(row => row.user !== 'Consolidate').map((userResponse, userIdx) => (
                    <div key={userIdx} className="w-100 mb-4">
                      <div className="d-flex justify-content-between align-items-center mb-2 p-2 bg-light rounded">
                        <h6 className="fw-bold mb-0">
                          User: {getUser(Number(userResponse.user))} | Entity: {userResponse.entity || 'N/A'}
                        </h6>
                       
                      </div>

                      {userResponse.answer && Array.isArray(userResponse.answer) && userResponse.answer.length > 0 ? (
                        <DataTable
                          value={userResponse.answer}
                          showGridlines
                          className="p-datatable-sm"
                          style={{ width: '100%' }}
                          scrollable
                          scrollHeight="200px"
                        >
                          {tableHeaders.map((header, headerIndex) => (
                            <Column
                              key={`user-${userIdx}-${header}-${headerIndex}`}
                              field={header}
                              header={header}
                              body={(rowData) => {
                                const cellData = rowData[header];
                                if (!cellData || !cellData.data) return '-';

                                const value = cellData.data.value;

                                switch (cellData.type) {
                                  case 1:
                                  case 2:
                                  case 3:
                                    return value || '-';
                                  case 4:
                                    if (cellData.data.values && value !== null && value !== undefined) {
                                      const selectedOption = cellData.data.values.find(opt => opt.value === value);
                                      return selectedOption ? selectedOption.label : value;
                                    }
                                    return value || '-';
                                  case 5:
                                    return cellData.data.label || '-';
                                  case 6:
                                    return value ? moment(value).format('DD-MM-YYYY') : '-';
                                  default:
                                    return value || '-';
                                }
                              }}
                            />
                          ))}
                        </DataTable>
                      ) : (
                        <div className="text-muted p-3 border rounded">
                          No data submitted by this user
                        </div>
                      )}
                    </div>
                  ));
                })()}
              </div>

            }
            <div className="table-responsive mb-3">
              <table className="table table-bordered">
                <thead className="table-light">
                  <tr>
                    <th>Consolidated Response</th>
                  </tr>
                </thead>
                <tbody>
                  {responsesToShow.map((row, idx) =>
                    row.user === 'Consolidate' ? (
                      <tr key={idx}>

                        <td>{renderAnswer(row.answer)}</td>
                      </tr>
                    ) : null
                  )}
                </tbody>
              </table>
            </div>

            {/* Input Label */}
            {inputTypes.includes(data.type) && (
                 <div className="d-flex justify-content-between align-items-center mb-2 p-2 bg-primary text-white rounded">
                    <h6 className="fw-bold mb-0 text-white">Enter Consolidated Response</h6>
                  </div>
            )}

            {/* Input Field */}
            <div className="mb-3">
              {data.type === 'text' && (
                <InputText className='col-5'
                  value={inputValue}
                  onChange={(e) => setInputValue(e.target.value)}
                />
              )}
              {data.type === 'textarea' && (
                <InputTextarea className='col-5'
                  rows={3}
                  value={inputValue}
                  onChange={(e) => setInputValue(e.target.value)}
                />
              )}
              {data.type === 'number' && (
                <InputNumber className='col-5'
                  min={0}

                  value={inputValue}
                  onChange={(e) => setInputValue(e.value)}
                />
              )}
              {data.type === 'date' && (
                <Calendar className='col-5'

                  value={inputValue}
                  onChange={(e) => setInputValue(e.value)}
                />
              )}
              {data.type === 'file' && (
                <input
                  className="form-control"
                  type="file"
                  onChange={(e) => { const form = new FormData(); form.append('file', e.target.files[0]); APIServices.post(API.FilesUpload, form).then((res) => { setInputValue(res?.data?.files?.[0]?.originalname) }) }}
                />
              )}
              {data.type === 'select' && (
                <Dropdown className='col-5'
                  value={
                    inputValue
                  }
                  options={data?.values || []}
                  onChange={(e) => {
                    setInputValue(e?.value || null);
                  }}
                />
              )}
              {['radio-group', 'checkpoint'].includes(data.type) && (
                <div>
                  {data.values.map((cb, cbind) => {
                    return (
                      <div className="p-2 flex text-justify fs-14 fw-5 align-items-center" >
                        <RadioButton inputId={"rg" + cbind} name={cb.label} value={cb.value} onChange={(e) => setInputValue(e.value)} checked={inputValue === cb.value} />

                        <label htmlFor={"rg" + cbind} className="ml-2">{cb.label}</label>
                      </div>
                    )
                  })}

                </div>
              )}
              {data.type === 'checkbox-group' && (
                <div>
                  {data.values.map((cb, cbind) => {
                    console.log(inputValue)
                    return (
                      <div className="flex text-justify fs-14 fw-5" style={{ marginBottom: 10 }}>
                        <Checkbox inputId={"cb" + cbind} name={cb.label} value={cb.value} onChange={() => {
                          const val = cb.value;
                          setInputValue(prev => {
                            if (!Array.isArray(prev)) prev = [];
                            return prev.includes(val)
                              ? prev.filter(v => v !== val)
                              : [...prev, val];
                          });
                        }} checked={Array.isArray(inputValue) && inputValue.includes(cb.value)} />
                        <label htmlFor={"cb" + cbind} className="ml-2">{cb.label}</label>
                      </div>
                    )
                  })

                  }

                </div>
              )}



              {/* Consolidated Response Section for tableadd */}
              {data.type === 'tableadd' && (
                <div className="mb-4">
                  {/* Show User Responses for tableadd */}


          

                  <div className="flex justify-content-end mb-2">
                    {(inputValue || []).length < (data.maxrowlimit || 10) && (
                      <Button
                        onClick={() => { addRow(data) }}
                        icon='pi pi-plus'
                        label="Add Row"
                        size="small"
                      />
                    )}
                  </div>

                  {(() => {
                    let tableHeaders = [];

                    // Extract headers for consolidated table
                    if (responsesToShow && responsesToShow.length > 0) {
                      const firstResponse = responsesToShow.find(r => r.answer && Array.isArray(r.answer) && r.answer.length > 0);
                      if (firstResponse && firstResponse.answer[0]) {
                        tableHeaders = Object.keys(firstResponse.answer[0]);
                      }
                    }
                    if (tableHeaders.length === 0 && inputValue && Array.isArray(inputValue) && inputValue.length > 0) {
                      tableHeaders = Object.keys(inputValue[0]);
                    }
                    if (tableHeaders.length === 0 && data.headers) {
                      tableHeaders = data.headers;
                    }

                    return (
                      <DataTable
                        scrollable
                        showGridlines
                        className="fullheight"
                        style={{ width: '100%', maxHeight: 300 }}
                        value={inputValue || []}
                        editMode="cell"
                        onCellEditInit={(e) => {
                          console.log('Cell edit init:', e);
                        }}
                        onCellEditCancel={(e) => {
                          console.log('Cell edit cancel:', e);
                        }}
                      >
                        {tableHeaders && tableHeaders.length > 0 ? (
                          tableHeaders.map((h, index) => {
                            return <Column
                              key={`consolidate-${data.name}-${h}-${index}`}
                              bodyClassName={(rowData) => {
                                return rowData[h] && rowData[h].type === 5 ? 'p-disabled' : ''
                              }}
                              header={h}
                              body={(rowData) => {
                                return renderTableData(rowData[h]);
                              }}
                              editor={(options) => {
                                // Create modified options with the correct field and value
                                const cellData = options.rowData[h];
                                const actualValue = cellData?.data?.value || '';
                                const modifiedOptions = {
                                  ...options,
                                  headerIndex: index,
                                  field: h,
                                  value: actualValue,
                                  rowData: options.rowData
                                };
                                return renderEditor(modifiedOptions);
                              }}
                              onCellEditComplete={(e) => {
                                // Create a custom event with the header field
                                const modifiedEvent = {
                                  ...e,
                                  headerIndex: index,
                                  field: h,
                                  cellIndex: index,
                                  columnField: h
                                };
                                onCellEditComplete(modifiedEvent);
                              }}
                            />;
                          })
                        ) : (
                          <Column header="No Headers Found" body={() => 'No data'} />
                        )}
                        <Column
                          header='Action'
                          style={{ width: 80 }}
                          body={(rowData, e) => { return actionTemplate(rowData, e) }}
                        />
                      </DataTable>
                    );
                  })()}
                </div>
              )}
            </div>

            {/* Comment Section */}
            <div className="mb-2 mt-3">
              <label className="form-label fw-bold">Comment / Remarks</label>
              <textarea
                className="form-control"
                rows={3}
                value={consolidateText}
                onChange={(e) => setConsolidateText(e.target.value)}
              />
            </div>

            {/* Buttons */}
            <div className="d-flex gap-2">
              <Button

                label='Save'
                onClick={() => { handleSave(data) }}
              />

              <Button
                text
                className='mandatory'
                onClick={handleCancel}
                label='Cancel'
              />

            </div>

          </div>
        </AccordionTab>
      </Accordion>
    </>
  );
};

export default SectionBox;
